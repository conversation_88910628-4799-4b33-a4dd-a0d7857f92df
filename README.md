# MM Speed Check Server 🚀

A comprehensive **Kotlin/Ktor** REST API for managing network speed test results in Myanmar. This server provides full CRUD operations with interactive API documentation, dummy data for development, and production-ready features.

## 📋 Table of Contents

- [Features](#-features)
- [Quick Start](#-quick-start)
- [API Documentation](#-api-documentation)
- [Database Schema](#-database-schema)
- [Development](#-development)
- [API Endpoints](#-api-endpoints)
- [Testing](#-testing)
- [Architecture](#-architecture)
- [Contributing](#-contributing)

## ✨ Features

### Core Functionality
- **Full CRUD Operations** - Create, Read, Update, Delete speed test results
- **RESTful API Design** - Clean, intuitive endpoints following REST principles
- **Interactive Documentation** - Swagger UI for easy API exploration
- **Dummy Data** - Pre-populated with realistic Myanmar ISP data
- **Error Handling** - Comprehensive error responses with proper HTTP status codes
- **CORS Support** - Ready for web application integration

### Technical Features
- **Kotlin Coroutines** - Asynchronous, non-blocking operations
- **H2 Database** - In-memory database for easy development
- **Exposed ORM** - Type-safe SQL DSL
- **JSON Serialization** - Automatic request/response conversion
- **Logging** - Comprehensive request/response logging
- **Status Pages** - Custom error page handling

### Myanmar ISP Support
Pre-loaded with data from major Myanmar telecommunications providers:
- **Ooredoo Myanmar** - 5G, 4G, 3G, WiFi, Fiber
- **MPT** (Myanmar Posts and Telecommunications) - 5G, 4G, 3G, WiFi, Fiber
- **Telenor Myanmar** - 5G, 4G, WiFi
- **MyTel** - 5G, 4G, WiFi

## 🚀 Quick Start

### Prerequisites
- **Java 17+** (OpenJDK recommended)
- **Git** for cloning the repository

### 1. Clone and Run
```bash
git clone <repository-url>
cd mmspeedcheck-server
./gradlew run
```

### 2. Verify Installation
```bash
curl http://localhost:8080/
# Expected: "Network Test Backend is running!"
```

### 3. Explore the API
- **Swagger UI**: http://localhost:8080/swagger
- **OpenAPI Spec**: http://localhost:8080/openapi
- **Health Check**: http://localhost:8080/

## 📚 API Documentation

### Interactive Documentation
Visit **http://localhost:8080/swagger** for the complete interactive API documentation with:
- Request/response examples
- Schema definitions
- Try-it-out functionality
- Error response documentation

### OpenAPI Specification
The complete OpenAPI 3.0 specification is available at **http://localhost:8080/openapi**

## 🗄️ Database Schema

### TestResults Table
| Column | Type | Description |
|--------|------|-------------|
| `id` | INTEGER | Primary key, auto-increment |
| `provider` | VARCHAR(50) | ISP name (e.g., "Ooredoo", "MPT") |
| `network_type` | VARCHAR(20) | Connection type ("5G", "4G", "3G", "WiFi", "Ethernet") |
| `download_speed_mbps` | DOUBLE | Download speed in Mbps |
| `upload_speed_mbps` | DOUBLE | Upload speed in Mbps |
| `ping_ms` | INTEGER | Ping latency in milliseconds |
| `timestamp` | VARCHAR(50) | ISO 8601 timestamp |

### Sample Data
The database is pre-populated with **20 realistic test records** covering:
- All major Myanmar ISPs
- Various network types (5G, 4G, 3G, WiFi, Ethernet)
- Different time periods and performance scenarios
- Peak hour variations and weekend performance

## 🛠️ Development

### Environment
Built with:

| Technology | Version |
|------------|---------|
| [Kotlin](https://kotlinlang.org/) | 2.1.10 |
| [Ktor](https://ktor.io/) | 3.2.3 |
| [Exposed](https://github.com/JetBrains/Exposed) | 0.61.0 |
| [H2 Database](https://www.h2database.com/) | 2.3.232 |

### Project Structure
```
src/main/kotlin/
├── Application.kt              # Main application entry point
├── db/
│   ├── DatabaseFactory.kt     # Database initialization
│   ├── DummyDataService.kt     # Sample data population
│   └── TestResults.kt          # Database table definition
├── plugin/
│   ├── ContentNegotiation.kt   # JSON serialization setup
│   ├── Cors.kt                 # CORS configuration
│   ├── Documentation.kt        # Swagger/OpenAPI setup
│   ├── Routing.kt              # API endpoints
│   └── ...                     # Other plugins
├── schema/
│   └── TestResultSchema.kt     # Data models and service layer
└── model/
    └── GeoInfo.kt              # Geolocation data models

src/main/resources/
├── application.yaml            # Application configuration
├── logback.xml                 # Logging configuration
└── openapi/
    └── documentation.yaml      # OpenAPI specification
```

### Building & Running

| Task | Description |
|------|-------------|
| `./gradlew run` | Run the development server |
| `./gradlew build` | Build the application |
| `./gradlew test` | Run the tests |
| `./gradlew buildFatJar` | Build executable JAR |

## 🔌 API Endpoints

### Test Results CRUD

#### Create Test Result
```bash
POST /test-results
Content-Type: application/json

{
  "provider": "Ooredoo",
  "networkType": "5G",
  "downloadSpeedMbps": 145.8,
  "uploadSpeedMbps": 78.2,
  "pingMs": 12,
  "timestamp": "2025-08-29T08:30:00Z"
}
```

#### Get All Test Results
```bash
GET /test-results
```

#### Get Test Result by ID
```bash
GET /test-results/{id}
```

#### Update Test Result
```bash
PUT /test-results/{id}
Content-Type: application/json

{
  "provider": "MPT",
  "networkType": "4G",
  "downloadSpeedMbps": 95.2,
  "uploadSpeedMbps": 48.7,
  "pingMs": 12,
  "timestamp": "2025-08-29T22:50:00Z"
}
```

#### Delete Test Result
```bash
DELETE /test-results/{id}
```

### Utility Endpoints

#### Health Check
```bash
GET /
# Returns: "Network Test Backend is running!"
```

#### IP Geolocation
```bash
GET /ip
# Returns geolocation info for client IP
```

## 🧪 Testing

### Manual Testing with cURL

1. **Start the server**:
```bash
./gradlew run
```

2. **Test all CRUD operations**:
```bash
# Health check
curl http://localhost:8080/

# Get all test results (should return 20 dummy records)
curl http://localhost:8080/test-results

# Get specific test result
curl http://localhost:8080/test-results/1

# Create new test result
curl -X POST http://localhost:8080/test-results \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "Ooredoo",
    "networkType": "5G",
    "downloadSpeedMbps": 150.0,
    "uploadSpeedMbps": 80.0,
    "pingMs": 10,
    "timestamp": "2025-08-29T12:00:00Z"
  }'

# Update test result
curl -X PUT http://localhost:8080/test-results/1 \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "MPT",
    "networkType": "4G",
    "downloadSpeedMbps": 95.0,
    "uploadSpeedMbps": 50.0,
    "pingMs": 20,
    "timestamp": "2025-08-29T13:00:00Z"
  }'

# Delete test result
curl -X DELETE http://localhost:8080/test-results/21
```

### Expected Server Output
When the server starts successfully, you'll see:
```
2025-08-29 22:49:32.062 [main] INFO  Application - Using embedded H2 database for testing
2025-08-29 22:49:32.103 [main] INFO  Application - Application started in 1.185 seconds.
2025-08-29 22:49:32.266 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
```

## 🏗️ Architecture

### Clean Architecture Principles
- **Separation of Concerns** - Clear separation between routing, business logic, and data access
- **Dependency Inversion** - Services depend on abstractions, not concrete implementations
- **Single Responsibility** - Each class has one reason to change

### Layer Structure
```
┌─────────────────────────────────────┐
│           API Layer                 │
│  (Routing, Serialization, CORS)    │
├─────────────────────────────────────┤
│         Service Layer               │
│    (TestResultService, Logic)      │
├─────────────────────────────────────┤
│          Data Layer                 │
│   (DatabaseFactory, Exposed ORM)   │
└─────────────────────────────────────┘
```

### Key Components
- **Application.kt** - Main entry point and plugin configuration
- **DatabaseFactory** - Database connection and schema management
- **TestResultService** - Business logic and data operations
- **Routing** - HTTP endpoint definitions and request handling
- **DummyDataService** - Development data population

## 🤝 Contributing

### Development Setup
1. **Clone the repository**
2. **Install Java 17+**
3. **Run the server**: `./gradlew run`
4. **Access documentation**: http://localhost:8080/swagger

### Code Style
- Follow Kotlin coding conventions
- Use meaningful variable and function names
- Add KDoc comments for public APIs
- Write tests for new functionality

### Adding New Features
1. **Database Changes**: Update `TestResults` table in `DatabaseFactory.kt`
2. **API Changes**: Add endpoints in `Routing.kt`
3. **Business Logic**: Implement in `TestResultService.kt`
4. **Documentation**: Update OpenAPI spec in `documentation.yaml`

### Useful Resources
- [Ktor Documentation](https://ktor.io/docs/home.html)
- [Exposed Documentation](https://github.com/JetBrains/Exposed/wiki)
- [Kotlin Coroutines Guide](https://kotlinlang.org/docs/coroutines-guide.html)
- [OpenAPI Specification](https://swagger.io/specification/)

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙋‍♂️ Support

For questions or issues:
1. Check the [Swagger documentation](http://localhost:8080/swagger)
2. Review the [OpenAPI specification](http://localhost:8080/openapi)
3. Create an issue in the repository

**Happy coding! 🚀**
2024-12-04 14:32:45.584 [main] INFO  Application - Application started in 0.303 seconds.
2024-12-04 14:32:45.682 [main] INFO  Application - Responding at http://0.0.0.0:8080
```

