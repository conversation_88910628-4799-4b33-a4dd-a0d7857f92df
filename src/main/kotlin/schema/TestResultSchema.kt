package com.htueko.schema

import com.htueko.db.DatabaseFactory
import com.htueko.db.TestResults
import kotlinx.coroutines.Dispatchers
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction

/**
 * Data class representing a test result request for creating/updating speed test data
 */
@Serializable
data class TestResultRequest(
    val provider: String,
    val networkType: String,
    val downloadSpeedMbps: Double,
    val uploadSpeedMbps: Double,
    val pingMs: Int,
    val timestamp: String
)

/**
 * Data class representing a test result response with ID for API responses
 */
@Serializable
data class TestResultResponse(
    val id: Int,
    val provider: String,
    val networkType: String,
    val downloadSpeedMbps: Double,
    val uploadSpeedMbps: Double,
    val pingMs: Int,
    val timestamp: String
)

/**
 * Service class for managing CRUD operations on TestResults table
 * Uses Exposed ORM for database operations with proper coroutine support
 */
class TestResultService {

    /**
     * Create a new test result record
     * @param testResult The test result data to create
     * @return The ID of the newly created record
     */
    suspend fun create(testResult: TestResultRequest): Int = dbQuery {
        TestResults.insert {
            it[provider] = testResult.provider
            it[networkType] = testResult.networkType
            it[downloadSpeedMbps] = testResult.downloadSpeedMbps
            it[uploadSpeedMbps] = testResult.uploadSpeedMbps
            it[pingMs] = testResult.pingMs
            it[timestamp] = testResult.timestamp
        }[TestResults.id]
    }

    /**
     * Read a single test result by ID
     * @param id The ID of the test result to retrieve
     * @return The test result if found, null otherwise
     */
    suspend fun read(id: Int): TestResultResponse? {
        return dbQuery {
            TestResults.selectAll()
                .where { TestResults.id eq id }
                .map { 
                    TestResultResponse(
                        id = it[TestResults.id],
                        provider = it[TestResults.provider],
                        networkType = it[TestResults.networkType],
                        downloadSpeedMbps = it[TestResults.downloadSpeedMbps],
                        uploadSpeedMbps = it[TestResults.uploadSpeedMbps],
                        pingMs = it[TestResults.pingMs],
                        timestamp = it[TestResults.timestamp]
                    )
                }
                .singleOrNull()
        }
    }

    /**
     * Read all test results
     * @return List of all test results
     */
    suspend fun readAll(): List<TestResultResponse> {
        return dbQuery {
            TestResults.selectAll().map {
                TestResultResponse(
                    id = it[TestResults.id],
                    provider = it[TestResults.provider],
                    networkType = it[TestResults.networkType],
                    downloadSpeedMbps = it[TestResults.downloadSpeedMbps],
                    uploadSpeedMbps = it[TestResults.uploadSpeedMbps],
                    pingMs = it[TestResults.pingMs],
                    timestamp = it[TestResults.timestamp]
                )
            }
        }
    }

    /**
     * Update an existing test result
     * @param id The ID of the test result to update
     * @param testResult The updated test result data
     */
    suspend fun update(id: Int, testResult: TestResultRequest) {
        dbQuery {
            TestResults.update({ TestResults.id eq id }) {
                it[provider] = testResult.provider
                it[networkType] = testResult.networkType
                it[downloadSpeedMbps] = testResult.downloadSpeedMbps
                it[uploadSpeedMbps] = testResult.uploadSpeedMbps
                it[pingMs] = testResult.pingMs
                it[timestamp] = testResult.timestamp
            }
        }
    }

    /**
     * Delete a test result by ID
     * @param id The ID of the test result to delete
     */
    suspend fun delete(id: Int) {
        dbQuery {
            TestResults.deleteWhere { TestResults.id.eq(id) }
        }
    }

    /**
     * Helper function to execute database queries with proper coroutine context
     */
    private suspend fun <T> dbQuery(block: suspend () -> T): T =
        newSuspendedTransaction(Dispatchers.IO, DatabaseFactory.database) { block() }
}
