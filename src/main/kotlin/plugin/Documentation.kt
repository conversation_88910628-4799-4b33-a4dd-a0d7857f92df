package com.htueko.plugin

import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.plugins.openapi.openAPI
import io.ktor.server.plugins.swagger.swaggerUI
import io.ktor.server.routing.routing

/**
 * Configures API documentation using Swagger UI and OpenAPI
 * Provides interactive API documentation at /swagger and /openapi endpoints
 */
fun Application.configureDocumentation() {
    routing {
        // Swagger UI endpoint - Interactive API documentation
        swaggerUI(path = "swagger", swaggerFile = "openapi/documentation.yaml") {
            version = "4.15.5"
        }
        
        // OpenAPI specification endpoint
        openAPI(path = "openapi", swaggerFile = "openapi/documentation.yaml")
    }
}
