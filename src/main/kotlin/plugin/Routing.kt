package com.htueko.plugin

import com.htueko.db.TestResults
import com.htueko.model.GeoInfo
import io.ktor.client.HttpClient
import io.ktor.client.engine.cio.CIO
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.content
import io.ktor.http.ContentType
import io.ktor.server.application.Application
import io.ktor.server.plugins.origin
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.response.respondText
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.routing
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction


private val httpClient = HttpClient(CIO)
fun Application.configureRouting() {
    routing {
        get("/") {
            call.respondText("Network Test Backend is running!", ContentType.Text.Plain)
        }

        get("/ip") {
            val ip = call.request.headers["X-Forwarded-For"] ?: call.request.origin.remoteAddress

            val httpResponse: HttpResponse = httpClient.get("http://ip-api.com/json/$ip")
            call.respond(httpResponse)
        }


        post("/test") {
            val testResult = call.receive<TestResultRequest>()
            val id = transaction {
                TestResults.insert {
                    it[provider] = testResult.provider
                    it[networkType] = testResult.networkType
                    it[downloadSpeedMbps] = testResult.downloadSpeedMbps
                    it[uploadSpeedMbps] = testResult.uploadSpeedMbps
                    it[pingMs] = testResult.pingMs
                    it[timestamp] = testResult.timestamp
                } get TestResults.id
            }
            call.respond(mapOf("id" to id))
        }

        get("/test") {
            val results = transaction {
                TestResults.selectAll().map {
                    TestResultResponse(
                        id = it[TestResults.id],
                        provider = it[TestResults.provider],
                        networkType = it[TestResults.networkType],
                        downloadSpeedMbps = it[TestResults.downloadSpeedMbps],
                        uploadSpeedMbps = it[TestResults.uploadSpeedMbps],
                        pingMs = it[TestResults.pingMs],
                        timestamp = it[TestResults.timestamp]
                    )
                }
            }
            call.respond(results)
        }

    }
}


@Serializable
data class TestResultRequest(
    val provider: String,
    val networkType: String,
    val downloadSpeedMbps: Double,
    val uploadSpeedMbps: Double,
    val pingMs: Int,
    val timestamp: String
)

@Serializable
data class TestResultResponse(
    val id: Int,
    val provider: String,
    val networkType: String,
    val downloadSpeedMbps: Double,
    val uploadSpeedMbps: Double,
    val pingMs: Int,
    val timestamp: String
)
