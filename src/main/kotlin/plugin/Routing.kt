package com.htueko.plugin

import com.htueko.model.GeoInfo
import com.htueko.schema.TestResultService
import com.htueko.schema.TestResultRequest
import io.ktor.client.HttpClient
import io.ktor.client.engine.cio.CIO
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.plugins.origin
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.response.respondText
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable

@Serializable
data class CreateResponse(val id: Int, val message: String)

@Serializable
data class ErrorResponse(val error: String)

@Serializable
data class MessageResponse(val message: String)


private val httpClient = HttpClient(CIO)

fun Application.configureRouting() {
    val testResultService = TestResultService()

    routing {
        get("/") {
            call.respondText("Network Test Backend is running!", ContentType.Text.Plain)
        }

        get("/ip") {
            val ip = call.request.headers["X-Forwarded-For"] ?: call.request.origin.remoteAddress
            val httpResponse: HttpResponse = httpClient.get("http://ip-api.com/json/$ip")
            call.respond(httpResponse)
        }


        // Create a new test result
        post("/test-results") {
            try {
                val testResult = call.receive<TestResultRequest>()
                val id = testResultService.create(testResult)
                call.respond(HttpStatusCode.Created, CreateResponse(id, "Test result created successfully"))
            } catch (e: Exception) {
                call.respond(HttpStatusCode.BadRequest, ErrorResponse("Failed to create test result: ${e.message}"))
            }
        }

        // Get all test results
        get("/test-results") {
            try {
                val results = testResultService.readAll()
                call.respond(HttpStatusCode.OK, results)
            } catch (e: Exception) {
                call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Failed to retrieve test results: ${e.message}"))
            }
        }

        // Get a single test result by ID
        get("/test-results/{id}") {
            try {
                val id = call.parameters["id"]?.toIntOrNull()
                    ?: return@get call.respond(HttpStatusCode.BadRequest, ErrorResponse("Invalid ID format"))

                val result = testResultService.read(id)
                if (result != null) {
                    call.respond(HttpStatusCode.OK, result)
                } else {
                    call.respond(HttpStatusCode.NotFound, ErrorResponse("Test result not found"))
                }
            } catch (e: Exception) {
                call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Failed to retrieve test result: ${e.message}"))
            }
        }

        // Update a test result
        put("/test-results/{id}") {
            try {
                val id = call.parameters["id"]?.toIntOrNull()
                    ?: return@put call.respond(HttpStatusCode.BadRequest, ErrorResponse("Invalid ID format"))

                val testResult = call.receive<TestResultRequest>()
                testResultService.update(id, testResult)
                call.respond(HttpStatusCode.OK, MessageResponse("Test result updated successfully"))
            } catch (e: Exception) {
                call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Failed to update test result: ${e.message}"))
            }
        }

        // Delete a test result
        delete("/test-results/{id}") {
            try {
                val id = call.parameters["id"]?.toIntOrNull()
                    ?: return@delete call.respond(HttpStatusCode.BadRequest, ErrorResponse("Invalid ID format"))

                testResultService.delete(id)
                call.respond(HttpStatusCode.OK, MessageResponse("Test result deleted successfully"))
            } catch (e: Exception) {
                call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Failed to delete test result: ${e.message}"))
            }
        }

    }
}



