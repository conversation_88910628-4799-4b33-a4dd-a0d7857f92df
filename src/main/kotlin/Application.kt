package com.htueko

import com.htueko.db.DatabaseFactory
import com.htueko.plugin.configureCORS
import com.htueko.plugin.configureDatabases
import com.htueko.plugin.configureDefaultHeaders
import com.htueko.plugin.configureMonitoring
import com.htueko.plugin.configureRouting
import com.htueko.plugin.configureSerialization
import com.htueko.plugin.configureStatusPages
import com.htueko.plugin.contentNegotiation
import io.ktor.server.application.Application

fun main(args: Array<String>) {
    io.ktor.server.netty.EngineMain.main(args)
}

fun Application.module() {
   // DatabaseFactory.init(this)
    contentNegotiation()
   // configureSerialization()
    configureRouting()
    configureMonitoring()
    configureDatabases()
    configureStatusPages()
    configureCORS()
    configureDefaultHeaders()
}
