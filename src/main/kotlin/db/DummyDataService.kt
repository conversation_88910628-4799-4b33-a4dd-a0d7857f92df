package com.htueko.db

import com.htueko.schema.TestResultRequest
import com.htueko.schema.TestResultService
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory

/**
 * Service for populating the database with dummy speed test data
 * Provides realistic test data for development and demonstration purposes
 */
object DummyDataService {
    private val logger = LoggerFactory.getLogger(DummyDataService::class.java)

    /**
     * Populates the database with sample speed test data
     * Creates diverse test results representing different ISPs, network types, and performance metrics
     */
    fun populateWithDummyData() {
        val testResultService = TestResultService()
        
        val dummyData = listOf(
            // Ooredoo Myanmar - Various network types
            TestResultRequest(
                provider = "Ooredoo",
                networkType = "5G",
                downloadSpeedMbps = 145.8,
                uploadSpeedMbps = 78.2,
                pingMs = 12,
                timestamp = "2025-08-29T08:30:00Z"
            ),
            TestResultRequest(
                provider = "Ooredoo",
                networkType = "4G",
                downloadSpeedMbps = 89.5,
                uploadSpeedMbps = 45.3,
                pingMs = 18,
                timestamp = "2025-08-29T09:15:00Z"
            ),
            TestResultRequest(
                provider = "Ooredoo",
                networkType = "WiFi",
                downloadSpeedMbps = 95.2,
                uploadSpeedMbps = 52.7,
                pingMs = 15,
                timestamp = "2025-08-29T10:45:00Z"
            ),
            
            // MPT (Myanmar Posts and Telecommunications) - National provider
            TestResultRequest(
                provider = "MPT",
                networkType = "4G",
                downloadSpeedMbps = 67.3,
                uploadSpeedMbps = 32.8,
                pingMs = 25,
                timestamp = "2025-08-29T11:20:00Z"
            ),
            TestResultRequest(
                provider = "MPT",
                networkType = "5G",
                downloadSpeedMbps = 128.7,
                uploadSpeedMbps = 65.4,
                pingMs = 14,
                timestamp = "2025-08-29T12:00:00Z"
            ),
            TestResultRequest(
                provider = "MPT",
                networkType = "WiFi",
                downloadSpeedMbps = 78.9,
                uploadSpeedMbps = 41.2,
                pingMs = 20,
                timestamp = "2025-08-29T13:30:00Z"
            ),
            
            // Telenor Myanmar - International provider
            TestResultRequest(
                provider = "Telenor",
                networkType = "5G",
                downloadSpeedMbps = 156.3,
                uploadSpeedMbps = 82.1,
                pingMs = 11,
                timestamp = "2025-08-29T14:15:00Z"
            ),
            TestResultRequest(
                provider = "Telenor",
                networkType = "4G",
                downloadSpeedMbps = 92.7,
                uploadSpeedMbps = 48.9,
                pingMs = 16,
                timestamp = "2025-08-29T15:45:00Z"
            ),
            TestResultRequest(
                provider = "Telenor",
                networkType = "WiFi",
                downloadSpeedMbps = 102.4,
                uploadSpeedMbps = 56.8,
                pingMs = 13,
                timestamp = "2025-08-29T16:20:00Z"
            ),
            
            // MyTel - Newer provider with competitive speeds
            TestResultRequest(
                provider = "MyTel",
                networkType = "5G",
                downloadSpeedMbps = 134.6,
                uploadSpeedMbps = 71.3,
                pingMs = 13,
                timestamp = "2025-08-29T17:00:00Z"
            ),
            TestResultRequest(
                provider = "MyTel",
                networkType = "4G",
                downloadSpeedMbps = 76.8,
                uploadSpeedMbps = 38.5,
                pingMs = 22,
                timestamp = "2025-08-29T18:30:00Z"
            ),
            
            // Fiber/Ethernet connections - Higher speeds, lower latency
            TestResultRequest(
                provider = "MPT Fiber",
                networkType = "Ethernet",
                downloadSpeedMbps = 245.7,
                uploadSpeedMbps = 198.3,
                pingMs = 8,
                timestamp = "2025-08-29T19:15:00Z"
            ),
            TestResultRequest(
                provider = "Ooredoo Fiber",
                networkType = "Ethernet",
                downloadSpeedMbps = 289.4,
                uploadSpeedMbps = 215.6,
                pingMs = 6,
                timestamp = "2025-08-29T20:00:00Z"
            ),
            
            // 3G connections - Lower speeds for comparison
            TestResultRequest(
                provider = "MPT",
                networkType = "3G",
                downloadSpeedMbps = 12.3,
                uploadSpeedMbps = 5.8,
                pingMs = 85,
                timestamp = "2025-08-29T21:30:00Z"
            ),
            TestResultRequest(
                provider = "Ooredoo",
                networkType = "3G",
                downloadSpeedMbps = 15.7,
                uploadSpeedMbps = 7.2,
                pingMs = 78,
                timestamp = "2025-08-29T22:15:00Z"
            ),
            
            // Recent tests with varying performance
            TestResultRequest(
                provider = "Telenor",
                networkType = "WiFi",
                downloadSpeedMbps = 87.6,
                uploadSpeedMbps = 44.3,
                pingMs = 17,
                timestamp = "2025-08-30T07:45:00Z"
            ),
            TestResultRequest(
                provider = "MyTel",
                networkType = "WiFi",
                downloadSpeedMbps = 93.8,
                uploadSpeedMbps = 49.7,
                pingMs = 19,
                timestamp = "2025-08-30T08:30:00Z"
            ),
            
            // Peak hour performance variations
            TestResultRequest(
                provider = "Ooredoo",
                networkType = "4G",
                downloadSpeedMbps = 72.4,
                uploadSpeedMbps = 35.9,
                pingMs = 28,
                timestamp = "2025-08-30T19:00:00Z"
            ),
            TestResultRequest(
                provider = "MPT",
                networkType = "5G",
                downloadSpeedMbps = 98.7,
                uploadSpeedMbps = 51.2,
                pingMs = 21,
                timestamp = "2025-08-30T20:30:00Z"
            ),
            
            // Weekend performance
            TestResultRequest(
                provider = "Telenor",
                networkType = "4G",
                downloadSpeedMbps = 105.3,
                uploadSpeedMbps = 58.7,
                pingMs = 14,
                timestamp = "2025-08-31T10:15:00Z"
            )
        )

        runBlocking {
            try {
                logger.info("Starting to populate database with ${dummyData.size} dummy records...")
                
                dummyData.forEachIndexed { index, testResult ->
                    val id = testResultService.create(testResult)
                    logger.debug("Created test result with ID: $id for ${testResult.provider} ${testResult.networkType}")
                }
                
                logger.info("Successfully populated database with ${dummyData.size} dummy speed test records")
                logger.info("Database now contains realistic test data from major Myanmar ISPs:")
                logger.info("- Ooredoo: 5G, 4G, 3G, WiFi, and Fiber connections")
                logger.info("- MPT: 5G, 4G, 3G, WiFi, and Fiber connections") 
                logger.info("- Telenor: 5G, 4G, and WiFi connections")
                logger.info("- MyTel: 5G, 4G, and WiFi connections")
                
            } catch (e: Exception) {
                logger.error("Failed to populate database with dummy data: ${e.message}", e)
                throw e
            }
        }
    }
}
