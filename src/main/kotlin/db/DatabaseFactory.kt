package com.htueko.db

import io.ktor.server.application.Application
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.Table

object TestResults : Table() {
    val id = integer("id").autoIncrement()
    val provider = varchar("provider", 50)
    val networkType = varchar("network_type", 20)  // WiFi, Mobile 4G, etc.
    val downloadSpeedMbps = double("download_speed_mbps")
    val uploadSpeedMbps = double("upload_speed_mbps")
    val pingMs = integer("ping_ms")
    val timestamp = varchar("timestamp", 50)  // Store ISO string or use datetime column with Exposed date/time extension

    override val primaryKey = PrimaryKey(id)
}

object DatabaseFactory {
    lateinit var database: Database
        private set

    fun init(application: Application) {
        // Use H2 in-memory database for easy setup without external dependencies
        val jdbcURL = "jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false"
        val driver = "org.h2.Driver"

        // Connect to the H2 database and store the reference
        database = Database.connect(jdbcURL, driver = driver)

        transaction(database) {
            SchemaUtils.create(TestResults)
        }
    }
}
