package com.htueko.db

import io.ktor.server.application.Application
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.Table

object TestResults : Table() {
    val id = integer("id").autoIncrement()
    val provider = varchar("provider", 50)
    val networkType = varchar("network_type", 20)  // WiFi, Mobile 4G, etc.
    val downloadSpeedMbps = double("download_speed_mbps")
    val uploadSpeedMbps = double("upload_speed_mbps")
    val pingMs = integer("ping_ms")
    val timestamp = varchar("timestamp", 50)  // Store ISO string or use datetime column with Exposed date/time extension

    override val primaryKey = PrimaryKey(id)
}

object DatabaseFactory {
    fun init(application: Application) {
//        val jdbcURL = "********************************************"
//        val driverClassName = "org.postgresql.Driver"
//        val user = "username"
//        val password = "password"
//
//        Database.connect(jdbcURL, driver = driverClassName, user = user, password = password)
//
//        transaction {
//            SchemaUtils.create(TestResults)
//        }

        // Read the configuration values from application.conf
        val config = application.environment.config.config("ktor.database")
        val jdbcURL = config.property("url").getString()
        val user = config.property("user").getString()
        val password = config.property("password").getString()

        // Connect to the database using the values from the configuration
        Database.connect(jdbcURL, driver = "org.postgresql.Driver", user = user, password = password)

        transaction {
            SchemaUtils.create(TestResults)
        }

    }
}
