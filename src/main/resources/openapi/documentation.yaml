openapi: 3.0.3
info:
  title: MM Speed Check Server API
  description: |
    A comprehensive API for managing network speed test results in Myanmar.
    
    This API provides full CRUD operations for speed test data including:
    - Creating new speed test results
    - Retrieving speed test data (all or by ID)
    - Updating existing speed test records
    - Deleting speed test records
    
    ## Features
    - RESTful API design
    - JSON request/response format
    - Comprehensive error handling
    - CORS support for web applications
    
    ## Database
    Uses H2 in-memory database for easy development and testing.
    
  version: 1.0.0
  contact:
    name: Developer
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080
    description: Development server

paths:
  /:
    get:
      summary: Health check endpoint
      description: Returns a simple message to verify the server is running
      responses:
        '200':
          description: Server is running
          content:
            text/plain:
              schema:
                type: string
                example: "Network Test Backend is running!"

  /test-results:
    get:
      summary: Get all test results
      description: Retrieves all speed test results from the database
      tags:
        - Test Results
      responses:
        '200':
          description: List of all test results
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TestResultResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      summary: Create a new test result
      description: Creates a new speed test result record
      tags:
        - Test Results
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestResultRequest'
      responses:
        '201':
          description: Test result created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateResponse'
        '400':
          description: Bad request - invalid input data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /test-results/{id}:
    get:
      summary: Get test result by ID
      description: Retrieves a specific speed test result by its ID
      tags:
        - Test Results
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the test result to retrieve
          schema:
            type: integer
            example: 1
      responses:
        '200':
          description: Test result found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestResultResponse'
        '404':
          description: Test result not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '400':
          description: Invalid ID format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      summary: Update test result
      description: Updates an existing speed test result
      tags:
        - Test Results
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the test result to update
          schema:
            type: integer
            example: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestResultRequest'
      responses:
        '200':
          description: Test result updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
        '400':
          description: Bad request - invalid input data or ID format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      summary: Delete test result
      description: Deletes a speed test result by its ID
      tags:
        - Test Results
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the test result to delete
          schema:
            type: integer
            example: 1
      responses:
        '200':
          description: Test result deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
        '400':
          description: Invalid ID format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /ip:
    get:
      summary: Get IP geolocation info
      description: Returns geolocation information for the client's IP address
      tags:
        - Utilities
      responses:
        '200':
          description: IP geolocation information
          content:
            application/json:
              schema:
                type: object
                properties:
                  query:
                    type: string
                    description: The IP address
                  country:
                    type: string
                    description: Country name
                  city:
                    type: string
                    description: City name
                  isp:
                    type: string
                    description: Internet Service Provider
                  org:
                    type: string
                    description: Organization

components:
  schemas:
    TestResultRequest:
      type: object
      required:
        - provider
        - networkType
        - downloadSpeedMbps
        - uploadSpeedMbps
        - pingMs
        - timestamp
      properties:
        provider:
          type: string
          description: Internet service provider name
          example: "Ooredoo"
        networkType:
          type: string
          description: Type of network connection
          example: "WiFi"
          enum: ["WiFi", "4G", "5G", "3G", "Ethernet"]
        downloadSpeedMbps:
          type: number
          format: double
          description: Download speed in Mbps
          example: 85.5
          minimum: 0
        uploadSpeedMbps:
          type: number
          format: double
          description: Upload speed in Mbps
          example: 42.3
          minimum: 0
        pingMs:
          type: integer
          description: Ping latency in milliseconds
          example: 15
          minimum: 0
        timestamp:
          type: string
          format: date-time
          description: ISO 8601 timestamp of the test
          example: "2025-08-29T22:47:00Z"

    TestResultResponse:
      allOf:
        - type: object
          properties:
            id:
              type: integer
              description: Unique identifier for the test result
              example: 1
        - $ref: '#/components/schemas/TestResultRequest'

    CreateResponse:
      type: object
      required:
        - id
        - message
      properties:
        id:
          type: integer
          description: ID of the created test result
          example: 1
        message:
          type: string
          description: Success message
          example: "Test result created successfully"

    MessageResponse:
      type: object
      required:
        - message
      properties:
        message:
          type: string
          description: Response message
          example: "Operation completed successfully"

    ErrorResponse:
      type: object
      required:
        - error
      properties:
        error:
          type: string
          description: Error message describing what went wrong
          example: "Test result not found"

tags:
  - name: Test Results
    description: Operations for managing speed test results
  - name: Utilities
    description: Utility endpoints for additional functionality
