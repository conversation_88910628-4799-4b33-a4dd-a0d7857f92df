# Deployment Guide

This guide covers different deployment options for the MM Speed Check Server.

## Table of Contents

- [Development Deployment](#development-deployment)
- [Production Deployment](#production-deployment)
- [Docker Deployment](#docker-deployment)
- [Database Configuration](#database-configuration)
- [Environment Variables](#environment-variables)
- [Monitoring and Logging](#monitoring-and-logging)

## Development Deployment

### Local Development
```bash
# Clone the repository
git clone <repository-url>
cd mmspeedcheck-server

# Run the development server
./gradlew run

# Server will be available at http://localhost:8080
```

### Development Features
- H2 in-memory database (data is lost on restart)
- Auto-populated with 20 dummy records
- Hot reload disabled (restart required for changes)
- Detailed logging enabled
- Swagger UI available at `/swagger`

## Production Deployment

### Prerequisites
- Java 17+ (OpenJDK recommended)
- PostgreSQL database (optional, can use H2)
- Reverse proxy (Nginx recommended)
- Process manager (systemd, PM2, or Docker)

### 1. Build Production JAR
```bash
./gradlew buildFatJar
```

This creates a self-contained JAR at `build/libs/mmspeedcheck-server-all.jar`

### 2. Production Configuration

Create `application-prod.yaml`:
```yaml
ktor:
  deployment:
    port: 8080
    host: 0.0.0.0
  application:
    modules:
      - com.htueko.ApplicationKt.module
  database:
    url: "*******************************************"
    user: "speedcheck_user"
    password: "secure_password"

logging:
  level:
    root: INFO
    com.htueko: INFO
```

### 3. Run Production Server
```bash
java -jar build/libs/mmspeedcheck-server-all.jar -config=application-prod.yaml
```

### 4. Systemd Service (Linux)

Create `/etc/systemd/system/mmspeedcheck.service`:
```ini
[Unit]
Description=MM Speed Check Server
After=network.target

[Service]
Type=simple
User=speedcheck
WorkingDirectory=/opt/mmspeedcheck
ExecStart=/usr/bin/java -jar mmspeedcheck-server-all.jar -config=application-prod.yaml
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl enable mmspeedcheck
sudo systemctl start mmspeedcheck
sudo systemctl status mmspeedcheck
```

## Docker Deployment

### 1. Create Dockerfile
```dockerfile
FROM openjdk:17-jre-slim

WORKDIR /app

# Copy the fat JAR
COPY build/libs/mmspeedcheck-server-all.jar app.jar

# Create non-root user
RUN addgroup --system speedcheck && adduser --system --group speedcheck
USER speedcheck

EXPOSE 8080

CMD ["java", "-jar", "app.jar"]
```

### 2. Build Docker Image
```bash
./gradlew buildFatJar
docker build -t mmspeedcheck-server .
```

### 3. Run with Docker
```bash
docker run -d \
  --name mmspeedcheck \
  -p 8080:8080 \
  mmspeedcheck-server
```

### 4. Docker Compose with PostgreSQL
Create `docker-compose.yml`:
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=************************************
      - DATABASE_USER=speedcheck
      - DATABASE_PASSWORD=password
    depends_on:
      - db

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=speedcheck
      - POSTGRES_USER=speedcheck
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

Run with:
```bash
docker-compose up -d
```

## Database Configuration

### PostgreSQL Setup

1. **Install PostgreSQL**:
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib
```

2. **Create Database and User**:
```sql
-- Connect as postgres user
sudo -u postgres psql

-- Create database and user
CREATE DATABASE speedcheck;
CREATE USER speedcheck_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE speedcheck TO speedcheck_user;
```

3. **Update Application Configuration**:
```yaml
ktor:
  database:
    url: "*******************************************"
    user: "speedcheck_user"
    password: "secure_password"
```

### H2 Database (Development)
For development or simple deployments, H2 can be used with persistent storage:

```yaml
ktor:
  database:
    url: "jdbc:h2:file:./data/speedcheck;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false"
    user: "sa"
    password: ""
```

## Environment Variables

### Supported Environment Variables
```bash
# Database configuration
DATABASE_URL=*******************************************
DATABASE_USER=speedcheck_user
DATABASE_PASSWORD=secure_password

# Server configuration
SERVER_PORT=8080
SERVER_HOST=0.0.0.0

# Logging
LOG_LEVEL=INFO
```

### Using Environment Variables
```bash
export DATABASE_URL="*******************************************"
export DATABASE_USER="speedcheck_user"
export DATABASE_PASSWORD="secure_password"

java -jar mmspeedcheck-server-all.jar
```

## Reverse Proxy Configuration

### Nginx Configuration
Create `/etc/nginx/sites-available/mmspeedcheck`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Optional: Serve static files directly
    location /swagger {
        proxy_pass http://localhost:8080/swagger;
    }

    location /openapi {
        proxy_pass http://localhost:8080/openapi;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/mmspeedcheck /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### SSL with Let's Encrypt
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## Monitoring and Logging

### Application Logs
Logs are written to stdout by default. In production, redirect to files:

```bash
java -jar mmspeedcheck-server-all.jar > /var/log/mmspeedcheck/app.log 2>&1
```

### Log Rotation
Configure logrotate in `/etc/logrotate.d/mmspeedcheck`:
```
/var/log/mmspeedcheck/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    copytruncate
}
```

### Health Monitoring
Set up monitoring for the health endpoint:

```bash
# Simple health check script
#!/bin/bash
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/)
if [ $response -eq 200 ]; then
    echo "Service is healthy"
    exit 0
else
    echo "Service is unhealthy (HTTP $response)"
    exit 1
fi
```

### Metrics Collection
Consider integrating with monitoring solutions:
- **Prometheus** - For metrics collection
- **Grafana** - For visualization
- **ELK Stack** - For log analysis

## Security Considerations

### Production Security Checklist
- [ ] Use HTTPS in production
- [ ] Implement rate limiting
- [ ] Add authentication if needed
- [ ] Use strong database passwords
- [ ] Keep dependencies updated
- [ ] Configure firewall rules
- [ ] Regular security updates
- [ ] Monitor for suspicious activity

### Firewall Configuration
```bash
# Allow HTTP and HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Allow SSH (be careful!)
sudo ufw allow 22

# Enable firewall
sudo ufw enable
```

## Troubleshooting

### Common Issues

1. **Port Already in Use**
```bash
# Find process using port 8080
sudo lsof -i :8080
# Kill the process
sudo kill -9 <PID>
```

2. **Database Connection Issues**
- Verify database is running
- Check connection credentials
- Ensure database exists
- Check firewall rules

3. **Memory Issues**
```bash
# Increase JVM heap size
java -Xmx1g -jar mmspeedcheck-server-all.jar
```

4. **Permission Issues**
```bash
# Ensure proper file permissions
chmod +x mmspeedcheck-server-all.jar
chown speedcheck:speedcheck mmspeedcheck-server-all.jar
```

### Log Analysis
```bash
# View recent logs
journalctl -u mmspeedcheck -f

# Search for errors
journalctl -u mmspeedcheck | grep ERROR

# View logs from specific time
journalctl -u mmspeedcheck --since "2025-08-29 10:00:00"
```

## Performance Tuning

### JVM Tuning
```bash
java -Xms512m -Xmx1g -XX:+UseG1GC -jar mmspeedcheck-server-all.jar
```

### Database Tuning
- Configure connection pooling
- Add database indexes for frequently queried fields
- Monitor query performance
- Regular database maintenance

### Caching
Consider adding caching for:
- Frequently accessed test results
- API responses
- Static content

This completes the deployment guide. The server is now ready for production deployment with proper monitoring, security, and scalability considerations.
