# API Usage Guide

This guide provides detailed examples and best practices for using the MM Speed Check Server API.

## Authentication

Currently, the API does not require authentication. All endpoints are publicly accessible.

## Base URL

```
http://localhost:8080
```

## Content Type

All requests and responses use JSON format:
```
Content-Type: application/json
```

## Error Handling

The API uses standard HTTP status codes and returns consistent error responses:

### Success Responses
- `200 OK` - Request successful
- `201 Created` - Resource created successfully

### Error Responses
- `400 Bad Request` - Invalid request data
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server error

### Error Response Format
```json
{
  "error": "Description of what went wrong"
}
```

## Rate Limiting

Currently, no rate limiting is implemented. In production, consider implementing rate limiting to prevent abuse.

## Data Validation

### Required Fields
All fields in `TestResultRequest` are required:
- `provider` (string, max 50 chars)
- `networkType` (string, max 20 chars)
- `downloadSpeedMbps` (double, >= 0)
- `uploadSpeedMbps` (double, >= 0)
- `pingMs` (integer, >= 0)
- `timestamp` (ISO 8601 string)

### Network Types
Supported network types:
- `"5G"` - Fifth generation mobile network
- `"4G"` - Fourth generation mobile network
- `"3G"` - Third generation mobile network
- `"WiFi"` - Wireless local area network
- `"Ethernet"` - Wired connection

### Myanmar ISP Providers
Common providers in Myanmar:
- `"Ooredoo"` - Ooredoo Myanmar
- `"MPT"` - Myanmar Posts and Telecommunications
- `"Telenor"` - Telenor Myanmar
- `"MyTel"` - MyTel (Viettel Myanmar)
- `"MPT Fiber"` - MPT Fiber service
- `"Ooredoo Fiber"` - Ooredoo Fiber service

## Example Workflows

### 1. Creating and Managing Test Results

```bash
# 1. Create a new test result
curl -X POST http://localhost:8080/test-results \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "Ooredoo",
    "networkType": "5G",
    "downloadSpeedMbps": 145.8,
    "uploadSpeedMbps": 78.2,
    "pingMs": 12,
    "timestamp": "2025-08-29T08:30:00Z"
  }'

# Response: {"id": 21, "message": "Test result created successfully"}

# 2. Retrieve the created result
curl http://localhost:8080/test-results/21

# 3. Update the result
curl -X PUT http://localhost:8080/test-results/21 \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "Ooredoo",
    "networkType": "5G",
    "downloadSpeedMbps": 150.0,
    "uploadSpeedMbps": 80.0,
    "pingMs": 10,
    "timestamp": "2025-08-29T08:35:00Z"
  }'

# 4. Delete the result
curl -X DELETE http://localhost:8080/test-results/21
```

### 2. Analyzing Speed Test Data

```bash
# Get all results for analysis
curl http://localhost:8080/test-results | jq '.'

# Filter results by provider (using jq)
curl http://localhost:8080/test-results | jq '.[] | select(.provider == "Ooredoo")'

# Get average download speed (using jq)
curl http://localhost:8080/test-results | jq '[.[] | .downloadSpeedMbps] | add / length'
```

## Best Practices

### 1. Timestamp Format
Always use ISO 8601 format for timestamps:
```json
{
  "timestamp": "2025-08-29T08:30:00Z"
}
```

### 2. Speed Measurements
- Use precise decimal values for speeds
- Ensure speeds are in Mbps (Megabits per second)
- Ping should be in milliseconds

### 3. Provider Names
- Use consistent provider names
- Consider standardizing provider names in your application

### 4. Error Handling
Always check HTTP status codes and handle errors appropriately:

```javascript
// JavaScript example
fetch('/test-results', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(testData)
})
.then(response => {
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  return response.json();
})
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));
```

## Integration Examples

### Web Application Integration
```html
<!DOCTYPE html>
<html>
<head>
    <title>Speed Test Results</title>
</head>
<body>
    <div id="results"></div>
    
    <script>
        // Fetch and display all test results
        fetch('http://localhost:8080/test-results')
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('results');
                data.forEach(result => {
                    resultsDiv.innerHTML += `
                        <div>
                            <h3>${result.provider} - ${result.networkType}</h3>
                            <p>Download: ${result.downloadSpeedMbps} Mbps</p>
                            <p>Upload: ${result.uploadSpeedMbps} Mbps</p>
                            <p>Ping: ${result.pingMs} ms</p>
                        </div>
                    `;
                });
            });
    </script>
</body>
</html>
```

### Mobile App Integration (Kotlin)
```kotlin
// Retrofit interface
interface SpeedTestApi {
    @GET("test-results")
    suspend fun getAllResults(): List<TestResultResponse>
    
    @POST("test-results")
    suspend fun createResult(@Body result: TestResultRequest): CreateResponse
}

// Usage
class SpeedTestRepository(private val api: SpeedTestApi) {
    suspend fun saveSpeedTest(result: TestResultRequest) {
        try {
            val response = api.createResult(result)
            Log.d("API", "Created result with ID: ${response.id}")
        } catch (e: Exception) {
            Log.e("API", "Failed to save result", e)
        }
    }
}
```

## Troubleshooting

### Common Issues

1. **400 Bad Request**
   - Check that all required fields are provided
   - Verify data types (numbers for speeds, string for provider)
   - Ensure timestamp is in ISO 8601 format

2. **404 Not Found**
   - Verify the test result ID exists
   - Check the endpoint URL is correct

3. **500 Internal Server Error**
   - Check server logs for detailed error information
   - Ensure database is properly initialized

### Debug Tips

1. **Use Swagger UI** - Visit http://localhost:8080/swagger for interactive testing
2. **Check Logs** - Server logs provide detailed error information
3. **Validate JSON** - Use tools like JSONLint to validate request bodies
4. **Test with cURL** - Use cURL commands to isolate issues

## Performance Considerations

- The H2 in-memory database is suitable for development but not production
- Consider implementing pagination for large datasets
- Add caching for frequently accessed data
- Monitor response times and optimize queries as needed
